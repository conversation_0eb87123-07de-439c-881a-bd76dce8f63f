/**
 * Camera 操作逻辑 Hook
 * 封装摄像头相关的业务操作
 */

import { useRef, useCallback } from 'react'
import { useToast } from '../contexts/ToastContext'
import { CAMERA_CONFIG, CAMERA_ERRORS, PHOTO_MODES, CAMERA_UTILS } from '../config/cameraConfig'
import {
  handleCameraError,
  checkCameraSupport,
  getAvailableCameras,
  validateVideoElement,
  safeStopStream,
  createCameraError
} from '../utils/errorHandling'

/**
 * 摄像头操作 Hook
 * @param {Object} cameraState 摄像头状态
 * @param {Function} dispatch 状态派发函数
 * @param {Object} actions Action creators
 * @returns {Object} 操作函数集合
 */
export function useCameraActions(cameraState, dispatch, actions) {
  const { showToast } = useToast()
  const videoRef = useRef(null)
  const setupTimeoutRef = useRef(null)

  // 防抖的摄像头设置函数
  const debouncedSetupCamera = useCallback((deviceId = null, mode = PHOTO_MODES.DOCUMENT) => {
    // 清除之前的定时器
    if (setupTimeoutRef.current) {
      clearTimeout(setupTimeoutRef.current)
    }

    // 设置新的定时器
    setupTimeoutRef.current = setTimeout(() => {
      setupCameraInternal(deviceId, mode)
    }, 200) // 200ms防抖
  }, [])

  // 内部摄像头设置函数
  const setupCameraInternal = useCallback(
    async (deviceId = null, mode = PHOTO_MODES.DOCUMENT) => {
      try {
        // 防止重复初始化同一个设备和模式
        if (
          cameraState.selectedCamera === deviceId &&
          cameraState.stream &&
          !cameraState.processing
        ) {
          return
        }

        // 检查浏览器支持
        if (!checkCameraSupport()) {
          throw createCameraError(CAMERA_ERRORS.BROWSER_NOT_SUPPORTED, '浏览器不支持摄像头功能')
        }

        // 获取可用设备
        const videoDevices = await getAvailableCameras()

        // 选择目标设备
        let targetDevice = deviceId
        if (!targetDevice) {
          if (videoDevices.length === 1) {
            targetDevice = videoDevices[0].deviceId
          } else {
            // 根据模式选择合适的设备
            if (mode === PHOTO_MODES.PORTRAIT) {
              targetDevice =
                videoDevices.find((d) =>
                  CAMERA_CONFIG.DEVICE_SELECTION.PORTRAIT_KEYWORDS.some((keyword) =>
                    d.label.includes(keyword)
                  )
                )?.deviceId || videoDevices[0].deviceId
            } else {
              targetDevice =
                videoDevices.find((d) =>
                  CAMERA_CONFIG.DEVICE_SELECTION.DOCUMENT_KEYWORDS.some((keyword) =>
                    d.label.startsWith(keyword)
                  )
                )?.deviceId || videoDevices[0].deviceId
            }
          }
        }

        // 停止当前流
        if (cameraState.stream) {
          safeStopStream(cameraState.stream)
        }

        // 获取分辨率设置
        const resolutionKey = mode.toUpperCase()
        const resolutionSettings =
          CAMERA_CONFIG.RESOLUTION[resolutionKey] || CAMERA_CONFIG.RESOLUTION.DOCUMENT

        // 请求媒体流
        const mediaStream = await navigator.mediaDevices.getUserMedia({
          video: {
            deviceId: targetDevice,
            ...resolutionSettings,
            frameRate: CAMERA_CONFIG.FRAME_RATE
          }
        })

        // 设置视频元素
        if (videoRef.current && mediaStream) {
          videoRef.current.srcObject = mediaStream

          // 等待视频加载完成
          await new Promise((resolve) => {
            videoRef.current.onloadedmetadata = () => {
              console.log('实际分辨率:', videoRef.current.videoWidth, videoRef.current.videoHeight)
              resolve()
            }
          })

          // 只有在成功设置视频后才更新状态
          dispatch(actions.setStream(mediaStream))
          dispatch(actions.setSelectedCamera(targetDevice))
        }
      } catch (error) {
        handleCameraError(error, showToast, { mode, deviceId })

        // 尝试降级配置
        if (error.name === CAMERA_ERRORS.OVERCONSTRAINED) {
          await setupCameraWithFallback(deviceId, mode)
        }
      }
    },
    [
      cameraState.stream,
      cameraState.selectedCamera,
      cameraState.processing,
      dispatch,
      actions,
      showToast
    ]
  )

  // 降级配置重试
  const setupCameraWithFallback = useCallback(
    async (deviceId, mode) => {
      try {
        const fallbackConfig = {
          video: {
            deviceId,
            ...CAMERA_CONFIG.RESOLUTION.FALLBACK
          }
        }

        const mediaStream = await navigator.mediaDevices.getUserMedia(fallbackConfig)
        if (videoRef.current) {
          videoRef.current.srcObject = mediaStream
          dispatch(actions.setStream(mediaStream))
          showToast('已使用降级配置启动摄像头', 'warning')
        }
      } catch (fallbackError) {
        handleCameraError(fallbackError, showToast, { mode, deviceId, fallback: true })
      }
    },
    [dispatch, actions, showToast]
  )

  // 拍照功能
  const takePhoto = useCallback(async () => {
    const video = videoRef.current

    try {
      // 验证视频状态
      validateVideoElement(video)

      dispatch(actions.setProcessing(true))

      const canvas = document.createElement('canvas')
      const videoWidth = video.videoWidth
      const videoHeight = video.videoHeight

      if (cameraState.mode === PHOTO_MODES.DOCUMENT) {
        // 文档模式：全图拍摄
        const targetWidth = CAMERA_CONFIG.TARGET_IMAGE_SIZE.DOCUMENT
        const targetHeight = (targetWidth * videoHeight) / videoWidth

        canvas.width = targetWidth
        canvas.height = targetHeight

        const context = canvas.getContext('2d')
        context.drawImage(video, 0, 0, videoWidth, videoHeight, 0, 0, targetWidth, targetHeight)

        const imageData = canvas.toDataURL('image/jpeg', CAMERA_CONFIG.IMAGE_QUALITY.DOCUMENT)
        const fileName = CAMERA_UTILS.generateFileName(cameraState.mode)

        dispatch(actions.showPreview({ data: imageData, fileName }))
      } else {
        // 证件和人像模式：裁剪拍摄
        const currentMode =
          cameraState.mode === PHOTO_MODES.IDCARD
            ? `idcard-${cameraState.idCardMode}`
            : cameraState.mode
        const cropStyle = CAMERA_CONFIG.CROP_STYLES[currentMode]

        if (!cropStyle) {
          throw createCameraError(CAMERA_ERRORS.CANVAS_ERROR, '未找到裁剪配置')
        }

        const cropWidthPercent = parseFloat(cropStyle.width) / 100
        const cropHeightPercent = parseFloat(cropStyle.height) / 100

        // 计算裁剪区域
        const cropWidth = videoWidth * cropWidthPercent
        const cropHeight = videoHeight * cropHeightPercent
        const cropX = (videoWidth - cropWidth) / 2
        const cropY = (videoHeight - cropHeight) / 2

        canvas.width = cropWidth
        canvas.height = cropHeight

        const context = canvas.getContext('2d')
        context.imageSmoothingEnabled = true
        context.imageSmoothingQuality = 'high'

        context.drawImage(video, cropX, cropY, cropWidth, cropHeight, 0, 0, cropWidth, cropHeight)

        const qualityKey = cameraState.mode.toUpperCase()
        const quality =
          CAMERA_CONFIG.IMAGE_QUALITY[qualityKey] || CAMERA_CONFIG.IMAGE_QUALITY.PORTRAIT
        const imageData = canvas.toDataURL('image/jpeg', quality)
        const fileName = CAMERA_UTILS.generateFileName(cameraState.mode, cameraState.idCardMode)

        dispatch(actions.showPreview({ data: imageData, fileName }))
      }
    } catch (error) {
      handleCameraError(error, showToast, {
        mode: cameraState.mode,
        idCardMode: cameraState.idCardMode
      })
    } finally {
      dispatch(actions.setProcessing(false))
    }
  }, [cameraState.mode, cameraState.idCardMode, dispatch, actions, showToast])

  // 开始拍照（启动倒计时）
  const startCapture = useCallback(() => {
    if (cameraState.canStartCapture) {
      dispatch(actions.startCountdown())
    }
  }, [cameraState.canStartCapture, dispatch, actions])

  // 重新拍摄
  const handleRetake = useCallback(async () => {
    dispatch(actions.hidePreview())
    dispatch(actions.resetCountdown())
    // 不需要重新初始化摄像头，因为摄像头应该已经在运行
    // 只有在没有流的情况下才重新初始化
    if (!cameraState.stream) {
      setTimeout(() => {
        debouncedSetupCamera(cameraState.selectedCamera, cameraState.mode)
      }, 100)
    }
  }, [
    dispatch,
    actions,
    debouncedSetupCamera,
    cameraState.selectedCamera,
    cameraState.mode,
    cameraState.stream
  ])

  // 设置模式
  const setMode = useCallback(
    (mode) => {
      // 只有当模式真正改变时才重新设置摄像头
      if (cameraState.mode !== mode) {
        dispatch(actions.setMode(mode))
        // 使用防抖函数避免状态更新冲突
        debouncedSetupCamera(null, mode)
      }
    },
    [cameraState.mode, dispatch, actions, debouncedSetupCamera]
  )

  // 设置身份证模式
  const setIdCardMode = useCallback(
    (idCardMode) => {
      dispatch(actions.setIdCardMode(idCardMode))
    },
    [dispatch, actions]
  )

  // 清理函数 - 组件卸载时调用
  const cleanup = useCallback(() => {
    if (setupTimeoutRef.current) {
      clearTimeout(setupTimeoutRef.current)
    }
  }, [])

  return {
    videoRef,
    setupCamera: debouncedSetupCamera,
    takePhoto,
    startCapture,
    handleRetake,
    setMode,
    setIdCardMode,
    cleanup
  }
}
