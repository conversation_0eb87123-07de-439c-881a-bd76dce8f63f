/**
 * 摄像头视图组件
 * 显示摄像头预览、控制按钮和倒计时
 */

import React, { useMemo } from 'react'
import { STYLES, PHOTO_MODES } from '../../config/cameraConfig'
import CropOverlay from './CropOverlay'

const CameraView = ({
  videoRef,
  mode,
  idCardMode,
  isCountingDown,
  countdown,
  onModeChange,
  onIdCardModeChange,
  onStartCapture,
  onClose,
  onCancelAutoSwitch
}) => {
  // 缓存样式对象
  const videoContainerStyle = useMemo(() => STYLES.VIDEO_CONTAINER, [])
  const videoStyle = useMemo(() => STYLES.VIDEO, [])

  return (
    <>
      {/* 摄像头预览区域 */}
      <div style={videoContainerStyle}>
        <video ref={videoRef} autoPlay playsInline style={videoStyle} />

        {/* 裁剪框 - 支持身份证和人像模式 */}
        {(mode === PHOTO_MODES.IDCARD || mode === PHOTO_MODES.PORTRAIT) && (
          <CropOverlay mode={mode} idCardMode={idCardMode} />
        )}

        {/* 倒计时显示 */}
        {isCountingDown && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-9xl font-bold text-white drop-shadow-[0_0_10px_rgba(0,0,0,0.5)]">
              {countdown}
            </div>
          </div>
        )}
      </div>

      {/* 操作按钮区域 */}
      {!isCountingDown && (
        <div className="mt-6 flex flex-col items-center gap-4">
          {/* 主要模式切换按钮组 */}
          <div className="flex justify-center gap-3 mb-2">
            <button
              onClick={() => {
                if (onCancelAutoSwitch) onCancelAutoSwitch()
                onModeChange(PHOTO_MODES.DOCUMENT)
              }}
              className={`btn-outline ${mode === PHOTO_MODES.DOCUMENT ? 'active' : ''}`}
            >
              文档模式
            </button>
            <button
              onClick={() => {
                if (onCancelAutoSwitch) onCancelAutoSwitch()
                onModeChange(PHOTO_MODES.IDCARD)
              }}
              className={`btn-outline ${mode === PHOTO_MODES.IDCARD ? 'active' : ''}`}
            >
              身份证模式
            </button>
            <button
              onClick={() => {
                if (onCancelAutoSwitch) onCancelAutoSwitch()
                onModeChange(PHOTO_MODES.PORTRAIT)
              }}
              className={`btn-outline ${mode === PHOTO_MODES.PORTRAIT ? 'active' : ''}`}
            >
              人像模式
            </button>
          </div>

          {/* 身份证正反面切换按钮 */}
          {mode === PHOTO_MODES.IDCARD && (
            <div className="flex justify-center gap-2 mb-2">
              <button
                onClick={() => onIdCardModeChange('front')}
                className={`px-4 py-2 rounded-lg text-sm transition-all duration-300 ${
                  idCardMode === 'front'
                    ? 'bg-blue-500 text-white shadow-md'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                正面
              </button>
              <button
                onClick={() => onIdCardModeChange('back')}
                className={`px-4 py-2 rounded-lg text-sm transition-all duration-300 ${
                  idCardMode === 'back'
                    ? 'bg-blue-500 text-white shadow-md'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                反面
              </button>
            </div>
          )}

          {/* 拍照和取消按钮 */}
          <div className="flex justify-center gap-6">
            <button onClick={onStartCapture} className="btn-primary">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-12"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"
                />
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"
                />
              </svg>
              开始拍照
            </button>
            <button onClick={onClose} className="btn-secondary">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
              取消
            </button>
          </div>
        </div>
      )}
    </>
  )
}

export default React.memo(CameraView)
