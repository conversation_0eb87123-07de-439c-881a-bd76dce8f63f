/**
 * Camera 组件状态管理
 * 使用 useReducer 统一管理组件状态
 */

import { CAMERA_CONFIG, INITIAL_PHOTOS_TAKEN, PHOTO_MODES, ID_CARD_MODES } from '../config/cameraConfig'

// Action Types
export const CAMERA_ACTIONS = {
  // 摄像头相关
  SET_STREAM: 'SET_STREAM',
  SET_SELECTED_CAMERA: 'SET_SELECTED_CAMERA',
  
  // 倒计时相关
  START_COUNTDOWN: 'START_COUNTDOWN',
  TICK_COUNTDOWN: 'TICK_COUNTDOWN',
  STOP_COUNTDOWN: 'STOP_COUNTDOWN',
  RESET_COUNTDOWN: 'RESET_COUNTDOWN',
  
  // 模式相关
  SET_MODE: 'SET_MODE',
  SET_ID_CARD_MODE: 'SET_ID_CARD_MODE',
  
  // 预览相关
  SHOW_PREVIEW: 'SHOW_PREVIEW',
  HIDE_PREVIEW: 'HIDE_PREVIEW',
  SET_PREVIEW_IMAGE: 'SET_PREVIEW_IMAGE',
  
  // 状态相关
  SET_PROCESSING: 'SET_PROCESSING',
  SET_AUTO_SWITCH_TIMER: 'SET_AUTO_SWITCH_TIMER',
  CLEAR_AUTO_SWITCH_TIMER: 'CLEAR_AUTO_SWITCH_TIMER',
  
  // 拍照记录相关
  MARK_PHOTO_TAKEN: 'MARK_PHOTO_TAKEN',
  RESET_PHOTOS_TAKEN: 'RESET_PHOTOS_TAKEN',
  
  // 重置状态
  RESET_STATE: 'RESET_STATE'
}

// 初始状态
export const initialCameraState = {
  // 摄像头相关
  stream: null,
  selectedCamera: null,
  
  // 倒计时相关
  countdown: CAMERA_CONFIG.TIMING.COUNTDOWN_DURATION,
  isCountingDown: false,
  
  // 模式相关
  mode: PHOTO_MODES.DOCUMENT,
  idCardMode: ID_CARD_MODES.FRONT,
  
  // 预览相关
  showPreview: false,
  previewImage: null,
  
  // 状态相关
  processing: false,
  autoSwitchTimer: null,
  
  // 拍照记录
  photosTaken: { ...INITIAL_PHOTOS_TAKEN }
}

// Action Creators
export const cameraActions = {
  setStream: (stream) => ({
    type: CAMERA_ACTIONS.SET_STREAM,
    payload: stream
  }),
  
  setSelectedCamera: (cameraId) => ({
    type: CAMERA_ACTIONS.SET_SELECTED_CAMERA,
    payload: cameraId
  }),
  
  startCountdown: () => ({
    type: CAMERA_ACTIONS.START_COUNTDOWN
  }),
  
  tickCountdown: () => ({
    type: CAMERA_ACTIONS.TICK_COUNTDOWN
  }),
  
  stopCountdown: () => ({
    type: CAMERA_ACTIONS.STOP_COUNTDOWN
  }),
  
  resetCountdown: () => ({
    type: CAMERA_ACTIONS.RESET_COUNTDOWN
  }),
  
  setMode: (mode) => ({
    type: CAMERA_ACTIONS.SET_MODE,
    payload: mode
  }),
  
  setIdCardMode: (idCardMode) => ({
    type: CAMERA_ACTIONS.SET_ID_CARD_MODE,
    payload: idCardMode
  }),
  
  showPreview: (imageData) => ({
    type: CAMERA_ACTIONS.SHOW_PREVIEW,
    payload: imageData
  }),
  
  hidePreview: () => ({
    type: CAMERA_ACTIONS.HIDE_PREVIEW
  }),
  
  setProcessing: (processing) => ({
    type: CAMERA_ACTIONS.SET_PROCESSING,
    payload: processing
  }),
  
  setAutoSwitchTimer: (timer) => ({
    type: CAMERA_ACTIONS.SET_AUTO_SWITCH_TIMER,
    payload: timer
  }),
  
  clearAutoSwitchTimer: () => ({
    type: CAMERA_ACTIONS.CLEAR_AUTO_SWITCH_TIMER
  }),
  
  markPhotoTaken: (photoKey) => ({
    type: CAMERA_ACTIONS.MARK_PHOTO_TAKEN,
    payload: photoKey
  }),
  
  resetPhotosTaken: () => ({
    type: CAMERA_ACTIONS.RESET_PHOTOS_TAKEN
  }),
  
  resetState: () => ({
    type: CAMERA_ACTIONS.RESET_STATE
  })
}

// Reducer
export function cameraReducer(state, action) {
  switch (action.type) {
    case CAMERA_ACTIONS.SET_STREAM:
      return {
        ...state,
        stream: action.payload
      }
      
    case CAMERA_ACTIONS.SET_SELECTED_CAMERA:
      return {
        ...state,
        selectedCamera: action.payload
      }
      
    case CAMERA_ACTIONS.START_COUNTDOWN:
      return {
        ...state,
        isCountingDown: true,
        countdown: CAMERA_CONFIG.TIMING.COUNTDOWN_DURATION
      }
      
    case CAMERA_ACTIONS.TICK_COUNTDOWN:
      return {
        ...state,
        countdown: Math.max(0, state.countdown - 1)
      }
      
    case CAMERA_ACTIONS.STOP_COUNTDOWN:
      return {
        ...state,
        isCountingDown: false
      }
      
    case CAMERA_ACTIONS.RESET_COUNTDOWN:
      return {
        ...state,
        countdown: CAMERA_CONFIG.TIMING.COUNTDOWN_DURATION,
        isCountingDown: false
      }
      
    case CAMERA_ACTIONS.SET_MODE:
      return {
        ...state,
        mode: action.payload,
        // 切换到身份证模式时重置为正面
        idCardMode: action.payload === PHOTO_MODES.IDCARD ? ID_CARD_MODES.FRONT : state.idCardMode
      }
      
    case CAMERA_ACTIONS.SET_ID_CARD_MODE:
      return {
        ...state,
        idCardMode: action.payload
      }
      
    case CAMERA_ACTIONS.SHOW_PREVIEW:
      return {
        ...state,
        showPreview: true,
        previewImage: action.payload
      }
      
    case CAMERA_ACTIONS.HIDE_PREVIEW:
      return {
        ...state,
        showPreview: false,
        previewImage: null
      }
      
    case CAMERA_ACTIONS.SET_PROCESSING:
      return {
        ...state,
        processing: action.payload
      }
      
    case CAMERA_ACTIONS.SET_AUTO_SWITCH_TIMER:
      return {
        ...state,
        autoSwitchTimer: action.payload
      }
      
    case CAMERA_ACTIONS.CLEAR_AUTO_SWITCH_TIMER:
      // 清理定时器
      if (state.autoSwitchTimer) {
        clearTimeout(state.autoSwitchTimer)
      }
      return {
        ...state,
        autoSwitchTimer: null
      }
      
    case CAMERA_ACTIONS.MARK_PHOTO_TAKEN:
      return {
        ...state,
        photosTaken: {
          ...state.photosTaken,
          [action.payload]: true
        }
      }
      
    case CAMERA_ACTIONS.RESET_PHOTOS_TAKEN:
      return {
        ...state,
        photosTaken: { ...INITIAL_PHOTOS_TAKEN }
      }
      
    case CAMERA_ACTIONS.RESET_STATE:
      // 清理定时器
      if (state.autoSwitchTimer) {
        clearTimeout(state.autoSwitchTimer)
      }
      // 停止媒体流
      if (state.stream) {
        state.stream.getTracks().forEach(track => track.stop())
      }
      return {
        ...initialCameraState
      }
      
    default:
      return state
  }
}

// 选择器函数（用于从状态中派生数据）
export const cameraSelectors = {
  // 检查是否所有照片都已拍摄
  areAllPhotosTaken: (state) => {
    return Object.values(state.photosTaken).every(taken => taken)
  },
  
  // 获取当前拍照键值
  getCurrentPhotoKey: (state) => {
    return state.mode === PHOTO_MODES.IDCARD ? `idcard-${state.idCardMode}` : state.mode
  },
  
  // 获取下一个拍摄模式
  getNextMode: (state) => {
    const currentIndex = CAMERA_CONFIG.SHOOTING_MODE_ORDER.indexOf(state.mode)
    if (currentIndex < CAMERA_CONFIG.SHOOTING_MODE_ORDER.length - 1) {
      return CAMERA_CONFIG.SHOOTING_MODE_ORDER[currentIndex + 1]
    }
    return null
  },
  
  // 检查是否可以开始拍照
  canStartCapture: (state) => {
    return state.stream && !state.isCountingDown && !state.processing && !state.showPreview
  }
}
