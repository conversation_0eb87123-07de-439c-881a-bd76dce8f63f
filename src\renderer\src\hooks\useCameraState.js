/**
 * Camera 状态管理 Hook
 * 封装摄像头组件的状态逻辑
 */

import { useReducer, useEffect } from 'react'
import { cameraReducer, initialCameraState, cameraActions, cameraSelectors } from '../reducers/cameraReducer'

/**
 * 摄像头状态管理 Hook
 * @returns {Object} 状态和派发函数
 */
export function useCameraState() {
  const [state, dispatch] = useReducer(cameraReducer, initialCameraState)

  // 计算派生状态
  const derivedState = {
    allPhotosTaken: cameraSelectors.areAllPhotosTaken(state),
    currentPhotoKey: cameraSelectors.getCurrentPhotoKey(state),
    nextMode: cameraSelectors.getNextMode(state),
    canStartCapture: cameraSelectors.canStartCapture(state)
  }

  // 组件卸载时清理资源
  useEffect(() => {
    return () => {
      dispatch(cameraActions.resetState())
    }
  }, [])

  return {
    state: { ...state, ...derivedState },
    dispatch,
    actions: cameraActions
  }
}

/**
 * 倒计时管理 Hook
 * @param {Object} cameraState 摄像头状态
 * @param {Function} dispatch 状态派发函数
 * @param {Function} onCountdownComplete 倒计时完成回调
 */
export function useCountdown(cameraState, dispatch, onCountdownComplete) {
  useEffect(() => {
    let timer
    
    if (cameraState.isCountingDown && cameraState.countdown > 0) {
      timer = setInterval(() => {
        dispatch(cameraActions.tickCountdown())
      }, 1000)
    } else if (cameraState.countdown === 0 && cameraState.isCountingDown) {
      // 倒计时结束，执行拍照
      dispatch(cameraActions.stopCountdown())
      if (onCountdownComplete) {
        onCountdownComplete()
      }
    }

    return () => {
      if (timer) {
        clearInterval(timer)
      }
    }
  }, [cameraState.isCountingDown, cameraState.countdown, dispatch, onCountdownComplete])
}

/**
 * 自动切换模式 Hook
 * @param {Object} cameraState 摄像头状态
 * @param {Function} dispatch 状态派发函数
 * @param {Function} setupCamera 摄像头设置函数
 */
export function useAutoModeSwitch(cameraState, dispatch, setupCamera) {
  // 启动自动切换
  const startAutoSwitch = (targetMode, delay = 1500) => {
    // 先清除之前的定时器
    if (cameraState.autoSwitchTimer) {
      dispatch(cameraActions.clearAutoSwitchTimer())
    }

    const switchTimer = setTimeout(() => {
      dispatch(cameraActions.setMode(targetMode))
      if (setupCamera) {
        setupCamera(null, targetMode)
      }
      dispatch(cameraActions.clearAutoSwitchTimer())
    }, delay)

    dispatch(cameraActions.setAutoSwitchTimer(switchTimer))
  }

  // 取消自动切换
  const cancelAutoSwitch = () => {
    dispatch(cameraActions.clearAutoSwitchTimer())
  }

  return {
    startAutoSwitch,
    cancelAutoSwitch
  }
}
