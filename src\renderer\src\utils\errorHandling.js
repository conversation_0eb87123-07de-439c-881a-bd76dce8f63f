/**
 * 摄像头错误处理工具
 * 提供统一的错误处理机制
 */

import { CAMERA_ERRORS, ERROR_MESSAGES } from '../config/cameraConfig'

/**
 * 创建自定义错误
 * @param {string} name 错误名称
 * @param {string} message 错误消息
 * @returns {Error} 自定义错误对象
 */
export function createCameraError(name, message) {
  const error = new Error(message)
  error.name = name
  return error
}

/**
 * 统一错误处理函数
 * @param {Error} error 错误对象
 * @param {Function} showToast Toast显示函数
 * @param {Object} context 错误上下文信息
 */
export function handleCameraError(error, showToast, context = {}) {
  console.error('摄像头错误:', error, context)

  // 获取错误消息
  const message = ERROR_MESSAGES[error.name] || `摄像头访问失败: ${error.message}`
  
  // 显示用户友好的错误提示
  showToast(message, 'error')

  // 记录错误到监控系统（如果存在）
  if (window.errorReporting) {
    window.errorReporting.captureException(error, {
      context: 'camera_operation',
      ...context
    })
  }

  // 返回错误信息供调用者使用
  return {
    type: error.name,
    message,
    originalError: error,
    context
  }
}

/**
 * 检查浏览器摄像头支持
 * @returns {boolean} 是否支持
 */
export function checkCameraSupport() {
  return !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia)
}

/**
 * 检查设备权限
 * @returns {Promise<boolean>} 权限状态
 */
export async function checkCameraPermission() {
  try {
    if (!navigator.permissions) {
      return true // 无法检查权限，假设有权限
    }
    
    const permission = await navigator.permissions.query({ name: 'camera' })
    return permission.state === 'granted'
  } catch (error) {
    console.warn('无法检查摄像头权限:', error)
    return true // 无法检查权限，假设有权限
  }
}

/**
 * 获取可用的摄像头设备
 * @returns {Promise<Array>} 摄像头设备列表
 */
export async function getAvailableCameras() {
  try {
    if (!checkCameraSupport()) {
      throw createCameraError(CAMERA_ERRORS.BROWSER_NOT_SUPPORTED, '浏览器不支持摄像头功能')
    }

    const devices = await navigator.mediaDevices.enumerateDevices()
    const videoDevices = devices.filter(device => device.kind === 'videoinput')

    if (videoDevices.length === 0) {
      throw createCameraError(CAMERA_ERRORS.DEVICE_NOT_FOUND, '未检测到摄像头设备')
    }

    return videoDevices
  } catch (error) {
    if (error.name in CAMERA_ERRORS) {
      throw error
    }
    throw createCameraError(CAMERA_ERRORS.DEVICE_NOT_FOUND, '获取摄像头设备失败')
  }
}

/**
 * 验证视频元素状态
 * @param {HTMLVideoElement} videoElement 视频元素
 * @returns {boolean} 是否准备就绪
 */
export function validateVideoElement(videoElement) {
  if (!videoElement) {
    throw createCameraError(CAMERA_ERRORS.VIDEO_NOT_READY, '视频元素不存在')
  }

  if (videoElement.readyState !== videoElement.HAVE_ENOUGH_DATA) {
    throw createCameraError(CAMERA_ERRORS.VIDEO_NOT_READY, '视频流未准备就绪')
  }

  return true
}

/**
 * 安全地停止媒体流
 * @param {MediaStream} stream 媒体流
 */
export function safeStopStream(stream) {
  if (stream && stream.getTracks) {
    try {
      stream.getTracks().forEach(track => {
        if (track.readyState === 'live') {
          track.stop()
        }
      })
    } catch (error) {
      console.warn('停止媒体流时出错:', error)
    }
  }
}

/**
 * 创建重试函数
 * @param {Function} fn 要重试的函数
 * @param {number} maxRetries 最大重试次数
 * @param {number} delay 重试延迟（毫秒）
 * @returns {Function} 带重试功能的函数
 */
export function createRetryFunction(fn, maxRetries = 3, delay = 1000) {
  return async function retryWrapper(...args) {
    let lastError
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await fn.apply(this, args)
      } catch (error) {
        lastError = error
        
        if (attempt === maxRetries) {
          break
        }
        
        // 某些错误不应该重试
        if (error.name === CAMERA_ERRORS.PERMISSION_DENIED) {
          break
        }
        
        // 等待后重试
        await new Promise(resolve => setTimeout(resolve, delay))
      }
    }
    
    throw lastError
  }
}

/**
 * 错误边界处理
 * @param {Function} fn 要包装的函数
 * @param {Function} onError 错误处理回调
 * @returns {Function} 包装后的函数
 */
export function withErrorBoundary(fn, onError) {
  return async function errorBoundaryWrapper(...args) {
    try {
      return await fn.apply(this, args)
    } catch (error) {
      if (onError) {
        onError(error)
      }
      throw error
    }
  }
}

/**
 * 超时处理包装器
 * @param {Function} fn 要包装的函数
 * @param {number} timeout 超时时间（毫秒）
 * @returns {Function} 带超时的函数
 */
export function withTimeout(fn, timeout = 10000) {
  return function timeoutWrapper(...args) {
    return Promise.race([
      fn.apply(this, args),
      new Promise((_, reject) => {
        setTimeout(() => {
          reject(createCameraError('TimeoutError', `操作超时 (${timeout}ms)`))
        }, timeout)
      })
    ])
  }
}
