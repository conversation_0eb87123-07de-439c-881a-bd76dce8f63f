/**
 * 拍照进度指示器组件
 * 显示各种拍照模式的完成状态
 */

import React from 'react'

const PhotoProgress = ({ photosTaken, allPhotosTaken, onComplete, menuLabel }) => {
  // 定义显示名称映射
  const getDisplayInfo = (type) => {
    const displayMap = {
      document: { name: '文档', title: '文档照片' },
      'idcard-front': { name: '身份证正面', title: '身份证正面照片' },
      'idcard-back': { name: '身份证反面', title: '身份证反面照片' },
      portrait: { name: '人像', title: '人像照片' }
    }
    return displayMap[type] || { name: type, title: type }
  }

  return (
    <div className="absolute top-1/2 -translate-y-1/2 left-8 z-10 bg-white/40 backdrop-blur-sm rounded-xl p-4 m-2 shadow-lg">
      <div className="space-y-3">
        {Object.entries(photosTaken).map(([type, taken]) => {
          const { name, title } = getDisplayInfo(type)

          return (
            <div key={type} className="flex items-center gap-2" title={title}>
              <svg
                className={`w-5 h-5 ${taken ? 'text-green-500' : 'text-gray-300'}`}
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                {taken ? (
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 13l4 4L19 7"
                  />
                ) : (
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                )}
              </svg>
              <span className={`text-sm ${taken ? 'text-green-600' : 'text-gray-500'}`}>
                {name}
              </span>
            </div>
          )
        })}
      </div>

      {/* 许可申请按钮 */}
      {allPhotosTaken && (
        <div className="mt-3 pt-3 border-t border-gray-200">
          <button onClick={onComplete} className="btn-primary">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
              />
            </svg>
            <span>{menuLabel}</span>
          </button>
        </div>
      )}
    </div>
  )
}

export default React.memo(PhotoProgress)
