/**
 * 照片预览组件
 * 显示拍摄的照片预览和操作按钮
 */

import React from 'react'
import { PHOTO_MODES } from '../../config/cameraConfig'

const PhotoPreview = ({ previewImage, mode, idCardMode, onSave, onRetake }) => {
  // 获取预览标题
  const getPreviewTitle = () => {
    switch (mode) {
      case PHOTO_MODES.DOCUMENT:
        return '文档照片预览'
      case PHOTO_MODES.IDCARD:
        return `身份证${idCardMode === 'front' ? '正面' : '反面'}预览`
      case PHOTO_MODES.PORTRAIT:
        return '人像照片预览'
      default:
        return '照片预览'
    }
  }

  if (!previewImage) return null

  return (
    <div className="flex flex-col items-center w-full max-w-5xl mx-auto">
      <div className="w-full bg-white rounded-2xl shadow-2xl overflow-hidden">
        <div className="bg-gradient-to-r from-teal-500 to-emerald-500 p-4 text-white">
          <h2 className="text-2xl font-semibold text-center">
            {getPreviewTitle()}
          </h2>
          <p className="text-sm opacity-80 text-center mt-1">
            {new Date().toLocaleString('zh-CN', {
              year: 'numeric',
              month: 'long',
              day: 'numeric',
              hour: '2-digit',
              minute: '2-digit'
            })}
          </p>
        </div>
        <div className="relative bg-gradient-to-b from-gray-50 to-gray-100 p-8 rounded-b-2xl">
          <img
            src={previewImage.data}
            alt="预览"
            className="w-full h-full object-contain rounded-lg shadow-lg"
          />
        </div>
      </div>
      
      <div className="mt-6 flex justify-center gap-6">
        <button onClick={onSave} className="btn-success">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-6 w-6"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M5 13l4 4L19 7"
            />
          </svg>
          保存照片
        </button>
        
        <button onClick={onRetake} className="btn-secondary">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-6 w-6"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
            />
          </svg>
          重新拍摄
        </button>
      </div>
    </div>
  )
}

export default React.memo(PhotoPreview)
