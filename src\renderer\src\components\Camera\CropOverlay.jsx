/**
 * 裁剪框覆盖层组件
 * 显示拍照时的裁剪框和遮罩
 */

import React, { useMemo } from 'react'
import { CAMERA_CONFIG, PHOTO_MODES } from '../../config/cameraConfig'

const CropOverlay = ({ mode, idCardMode }) => {
  // 计算当前裁剪样式
  const currentCropStyle = useMemo(() => {
    const currentCropMode = mode === PHOTO_MODES.IDCARD ? `idcard-${idCardMode}` : mode
    return CAMERA_CONFIG.CROP_STYLES[currentCropMode]
  }, [mode, idCardMode])

  // 获取提示文字
  const getHintText = () => {
    if (mode === PHOTO_MODES.IDCARD) {
      return `请将身份证${idCardMode === 'front' ? '正面' : '反面'}放入框内`
    }
    return '请将头部放入框内'
  }

  if (!currentCropStyle) return null

  return (
    <>
      {/* 模糊遮罩 */}
      <div className="absolute inset-0">
        {/* 上方模糊区域 */}
        <div
          className="absolute backdrop-blur-sm bg-black/30"
          style={{
            top: 0,
            left: 0,
            right: 0,
            height: `calc(50% - ${parseFloat(currentCropStyle.height) / 2}%)`
          }}
        />
        {/* 下方模糊区域 */}
        <div
          className="absolute backdrop-blur-sm bg-black/30"
          style={{
            bottom: 0,
            left: 0,
            right: 0,
            height: `calc(50% - ${parseFloat(currentCropStyle.height) / 2}%)`
          }}
        />
        {/* 左侧模糊区域 */}
        <div
          className="absolute backdrop-blur-sm bg-black/30"
          style={{
            top: `calc(50% - ${parseFloat(currentCropStyle.height) / 2}%)`,
            bottom: `calc(50% - ${parseFloat(currentCropStyle.height) / 2}%)`,
            left: 0,
            width: `calc(50% - ${parseFloat(currentCropStyle.width) / 2}%)`
          }}
        />
        {/* 右侧模糊区域 */}
        <div
          className="absolute backdrop-blur-sm bg-black/30"
          style={{
            top: `calc(50% - ${parseFloat(currentCropStyle.height) / 2}%)`,
            bottom: `calc(50% - ${parseFloat(currentCropStyle.height) / 2}%)`,
            right: 0,
            width: `calc(50% - ${parseFloat(currentCropStyle.width) / 2}%)`
          }}
        />
      </div>

      {/* 裁剪框 */}
      <div
        className="absolute border-2 border-yellow-400 border-dashed pointer-events-none z-10"
        style={{
          width: currentCropStyle.width,
          height: currentCropStyle.height,
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          transition: 'all 0.3s ease'
        }}
      >
        {/* 四角标记 */}
        <div className="absolute top-0 left-0 w-6 h-6 border-l-4 border-t-4 border-white"></div>
        <div className="absolute top-0 right-0 w-6 h-6 border-r-4 border-t-4 border-white"></div>
        <div className="absolute bottom-0 left-0 w-6 h-6 border-l-4 border-b-4 border-white"></div>
        <div className="absolute bottom-0 right-0 w-6 h-6 border-r-4 border-b-4 border-white"></div>

        {/* 辅助线 */}
        <div className="absolute top-1/3 left-0 right-0 border-t border-white opacity-30"></div>
        <div className="absolute top-2/3 left-0 right-0 border-t border-white opacity-30"></div>
        <div className="absolute left-1/3 top-0 bottom-0 border-l border-white opacity-30"></div>
        <div className="absolute left-2/3 top-0 bottom-0 border-l border-white opacity-30"></div>

        {/* 提示文字 */}
        <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 text-white text-sm px-2 py-1 rounded bg-black bg-opacity-50">
          {getHintText()}
        </div>
      </div>
    </>
  )
}

export default React.memo(CropOverlay)
