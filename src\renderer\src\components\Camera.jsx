import { useEffect, useCallback } from 'react'
import { useToast } from '../contexts/ToastContext'
import { useCameraState, useCountdown, useAutoModeSwitch } from '../hooks/useCameraState'
import { useCameraActions } from '../hooks/useCameraActions'
import { CAMERA_UTILS, PHOTO_MODES } from '../config/cameraConfig'

// 子组件导入
import ProcessingOverlay from './Camera/ProcessingOverlay'
import PhotoProgress from './Camera/PhotoProgress'
import PhotoPreview from './Camera/PhotoPreview'
import CameraView from './Camera/CameraView'

function Camera({ onClose, onCapture, onPhotoComplete, menuLabel }) {
  const { showToast } = useToast()

  // 使用自定义状态管理 Hook
  const { state, dispatch, actions } = useCameraState()

  // 使用摄像头操作 Hook
  const cameraActions = useCameraActions(state, dispatch, actions)

  // 使用自动模式切换 Hook
  const { startAutoSwitch, cancelAutoSwitch } = useAutoModeSwitch(
    state,
    dispatch,
    cameraActions.setupCamera
  )

  // 使用倒计时 Hook
  useCountdown(state, dispatch, cameraActions.takePhoto)

  // 处理保存照片
  const handleSave = useCallback(() => {
    if (state.previewImage) {
      onCapture(state.previewImage)

      // 更新拍照状态
      const photoKey = CAMERA_UTILS.getPhotoKey(state.mode, state.idCardMode)
      dispatch(actions.markPhotoTaken(photoKey))

      // 检查是否所有照片都已完成
      const updatedPhotosTaken = {
        ...state.photosTaken,
        [photoKey]: true
      }
      const allCompleted = CAMERA_UTILS.areAllPhotosTaken(updatedPhotosTaken)

      // 身份证拍照完成后的逻辑
      if (state.mode === PHOTO_MODES.IDCARD) {
        if (state.idCardMode === 'front') {
          // 拍完正面，自动切换到反面
          showToast('身份证正面拍照完成', 'success')
          setTimeout(() => {
            dispatch(actions.setIdCardMode('back'))
          }, 1500)
        } else {
          // 拍完反面，检查是否需要跳转到下一个模式
          showToast('身份证反面拍照完成', 'success')
          const nextMode = CAMERA_UTILS.getNextMode(state.mode)
          if (nextMode) {
            startAutoSwitch(nextMode)
          } else if (allCompleted) {
            onPhotoComplete()
          }
        }
      } else {
        // 其他模式拍照完成后的逻辑
        showToast('照片保存成功', 'success')
        const nextMode = CAMERA_UTILS.getNextMode(state.mode)
        if (nextMode) {
          startAutoSwitch(nextMode)
        } else if (allCompleted) {
          onPhotoComplete()
        }
      }

      // 关闭预览
      dispatch(actions.hidePreview())
      dispatch(actions.resetCountdown())
    }
  }, [
    state.previewImage,
    state.mode,
    state.idCardMode,
    state.photosTaken,
    dispatch,
    actions,
    onCapture,
    onPhotoComplete,
    showToast,
    startAutoSwitch
  ])

  // 初始化摄像头 - 只在组件挂载时执行一次
  useEffect(() => {
    cameraActions.setupCamera(null, state.mode)
    return () => {
      // 组件卸载时清理资源
      dispatch(actions.resetState())
    }
  }, []) // 空依赖数组，只在挂载时执行

  // 监听预览状态变化 - 移除setupCamera依赖避免循环
  useEffect(() => {
    if (!state.showPreview && state.selectedCamera && !state.stream) {
      // 当退出预览模式且没有视频流时，重新初始化摄像头
      // 使用setTimeout避免立即执行导致的状态冲突
      const timer = setTimeout(() => {
        cameraActions.setupCamera(state.selectedCamera, state.mode)
      }, 200)

      return () => clearTimeout(timer)
    }
  }, [state.showPreview, state.stream]) // 监听showPreview和stream变化

  return (
    <div className="fixed inset-0 bg-black bg-opacity-80 flex items-center justify-center z-50">
      {/* 处理状态遮罩 */}
      <ProcessingOverlay visible={state.processing} />

      <div className="bg-white rounded-2xl p-4 w-full relative">
        {/* 拍照进度指示器 */}
        <PhotoProgress
          photosTaken={state.photosTaken}
          allPhotosTaken={state.allPhotosTaken}
          onComplete={onPhotoComplete}
          menuLabel={menuLabel}
        />

        {state.showPreview ? (
          <PhotoPreview
            previewImage={state.previewImage}
            mode={state.mode}
            idCardMode={state.idCardMode}
            onSave={handleSave}
            onRetake={cameraActions.handleRetake}
          />
        ) : (
          <CameraView
            videoRef={cameraActions.videoRef}
            mode={state.mode}
            idCardMode={state.idCardMode}
            isCountingDown={state.isCountingDown}
            countdown={state.countdown}
            onModeChange={cameraActions.setMode}
            onIdCardModeChange={cameraActions.setIdCardMode}
            onStartCapture={cameraActions.startCapture}
            onClose={onClose}
            onCancelAutoSwitch={cancelAutoSwitch}
          />
        )}
      </div>
    </div>
  )
}

export default Camera
