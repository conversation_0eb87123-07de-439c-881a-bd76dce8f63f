import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import axios from 'axios'
import Camera from '../components/Camera'
import PageLayout from '../components/PageLayout'
import { useToast } from '../contexts/ToastContext'
import { useNetwork } from '../contexts/NetworkContext'

const http = axios.create({
  baseURL: 'https://smart.yukunnengyuan.com/api',
  timeout: 5000
})

function Home() {
  const navigate = useNavigate()
  const [title, setTitle] = useState('')
  const [menus, setMenus] = useState([])
  const [bgImage, setbgImage] = useState('')
  const [showCamera, setShowCamera] = useState(false)
  const [currentPhotoMenu, setCurrentPhotoMenu] = useState(null)
  const [isLoading, setIsLoading] = useState(true)
  const { showToast } = useToast()
  const { isOnline } = useNetwork()

  /**
   * 获取应用配置和菜单数据
   */
  const fetchConfig = async () => {
    if (!isOnline) {
      setIsLoading(false)
      return
    }

    try {
      setIsLoading(true)
      const appConfig = await window.api.getAppConfig()

      const response = await http.get('/client/get_menu', {
        params: {
          id: appConfig.id
        }
      })

      const data = response.data.data

      setTitle(data.title)
      setMenus(data.menus)
      setbgImage(data.bgImage)
    } catch (error) {
      console.error('获取配置失败:', error)
      showToast('获取页面数据失败，请检查网络连接', 'error')
    } finally {
      setIsLoading(false)
    }
  }

  // 初始化时获取配置
  useEffect(() => {
    fetchConfig()
  }, [])

  // 监听网络状态变化，网络恢复时重新获取数据
  useEffect(() => {
    if (isOnline && menus.length === 0) {
      fetchConfig()
    }
  }, [isOnline])

  // 监听网络恢复事件，主动重新加载数据
  useEffect(() => {
    const handleNetworkRecovered = () => {
      console.log('收到网络恢复事件，重新加载数据')
      fetchConfig()
    }

    window.addEventListener('network-recovered', handleNetworkRecovered)

    return () => {
      window.removeEventListener('network-recovered', handleNetworkRecovered)
    }
  }, [])

  /**
   * 处理菜单项点击事件
   * @param {Object} item - 菜单项对象
   */
  const handleMenuClick = (item) => {
    console.log(item)
    if (item.type === '链接') {
      // 直接打开链接，网络状态由模态框统一处理
      window.api.openExternal(item.target)
    } else if (item.type === '拍照') {
      setCurrentPhotoMenu(item)
      setShowCamera(true)
    } else if (item.type === '文本') {
      const params = new URLSearchParams([
        ['title', item.title],
        ['bgImage', item.bgImage],
        ['content', item.content]
      ])
      navigate(`/text?${params}`)
    } else if (item.type === '图片') {
      const params = new URLSearchParams([
        ['bgImage', item.bgImage],
        ['image', item.image]
      ])
      navigate(`/image?${params}`)
    } else if (item.type === 'pdf列表') {
      const params = new URLSearchParams([
        ['bgImage', item.bgImage],
        ['title', item.label],
        ['data', JSON.stringify(item.data)]
      ])
      navigate(`/PdfList?${params.toString()}`)
    } else if (item.type === '文件') {
      window.api.openLocalFile(item.target)
    }
  }

  const handleCapture = async (imageData) => {
    try {
      if (!imageData || !imageData.data || !imageData.fileName) {
        showToast('无效的图片数据', 'error')
        return
      }

      console.log('准备保存图片:', {
        fileName: imageData.fileName,
        dataLength: imageData.data?.length
      })

      const result = await window.api.saveImage(imageData)
      console.log('保存结果:', result)

      if (result && result.success) {
        showToast('照片保存成功！', 'success')
      } else {
        const errorMessage = result?.error || '未知错误'
        console.error('保存失败详情:', { result })
        showToast(`保存失败: ${errorMessage}`, 'error')
      }
    } catch (error) {
      console.error('保存照片失败:', {
        message: error.message,
        stack: error.stack,
        error: error
      })
      showToast(`保存照片失败: ${error.message || '未知错误'}`, 'error')
    }
  }
  const handlePhotoComplete = () => {
    if (currentPhotoMenu && currentPhotoMenu.target) {
      window.api.openExternal(currentPhotoMenu.target)
    }
    setShowCamera(false)
    setCurrentPhotoMenu(null)
  }
  return (
    <div
      className="min-h-screen flex flex-col items-center justify-start bg-cover bg-center"
      style={bgImage ? { backgroundImage: `url(${bgImage})` } : {}}
    >
      {/* 加载状态显示 */}
      {isLoading && (
        <div className="flex flex-col items-center justify-center mt-[60rem]">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-white mb-4"></div>
          <p className="text-white text-xl font-semibold">正在加载页面数据...</p>
        </div>
      )}

      {/* 网络断开且无数据时的提示 */}
      {!isLoading && !isOnline && menus.length === 0 && (
        <div className="flex flex-col items-center justify-center mt-[60rem]">
          <div className="text-white text-center">
            <div className="text-6xl mb-4">📡</div>
            <p className="text-xl font-semibold mb-2">网络连接断开</p>
            <p className="text-lg opacity-80">请检查网络连接后重试</p>
          </div>
        </div>
      )}

      {/* 菜单显示 */}
      {!isLoading && menus.length > 0 && (
        <div className="grid grid-cols-2 gap-24 w-full max-w-4xl mt-[60rem]">
          {menus.map((item) => (
            <button
              key={item.key}
              className="text-4xl text-white font-bold tracking-[1.3rem] bg-teal-500 py-4 px-8 
                rounded-lg hover:bg-teal-600 transition-all duration-300 ease-in-out 
                border border-white
                [background:radial-gradient(circle,rgba(7,122,92),rgba(7,122,92,0.5))]
                [clip-path:polygon(5%_0%,100%_0%,100%_70%,95%_100%,0%_100%,0%_30%)]
                shadow-lg hover:shadow-xl transform hover:scale-105"
              onClick={() => handleMenuClick(item)}
            >
              {item.label}
            </button>
          ))}
        </div>
      )}

      {showCamera && currentPhotoMenu && (
        <Camera
          onClose={() => {
            setShowCamera(false)
            setCurrentPhotoMenu(null)
          }}
          onCapture={handleCapture}
          onPhotoComplete={handlePhotoComplete}
          menuLabel={currentPhotoMenu.label}
        />
      )}
    </div>
  )
}

export default Home
